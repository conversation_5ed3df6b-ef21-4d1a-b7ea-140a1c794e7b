import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Calendar, Clock, MapPin, Users, Timer, AlertCircle, Trophy } from 'lucide-react';
import { InfoCard, InfoGrid, InfoItem, StatusBadge } from '@/components/ui/info-card';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/custom-card';

const meta: Meta<typeof InfoCard> = {
  title: 'UI/Enhanced Custom Cards',
  component: InfoCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Moderne, responsive Custom Cards mit Glassmorphism-Design für optimale Darstellung von Spielinformationen.',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'glass', 'minimal'],
      description: 'Design-Variante der Card',
    },
    interactive: {
      control: 'boolean',
      description: 'Aktiviert Hover-Effekte und Interaktivität',
    },
  },
};

export default meta;
type Story = StoryObj<typeof InfoCard>;

// Basis InfoCard Story
export const Default: Story = {
  args: {
    variant: 'elevated',
    interactive: true,
    title: 'Nächstes Spiel',
    titleIcon: <Calendar className="h-5 w-5" />,
    badge: <StatusBadge status="info" size="sm">Anmeldung geöffnet</StatusBadge>,
  },
  render: (args) => (
    <div className="max-w-2xl">
      <InfoCard {...args}>
        <div className="text-sm text-muted-foreground mb-4">
          Informationen zum nächsten Spiel
        </div>
        <InfoGrid columns={2}>
          <InfoItem
            icon={<Calendar className="h-4 w-4" />}
            label="Datum & Uhrzeit"
            value="Freitag, 6. Juni 2025 um 21:00"
          />
          <InfoItem
            icon={<MapPin className="h-4 w-4" />}
            label="Spielort"
            value="Soccerhalle Niederrhein"
          />
          <InfoItem
            icon={<Clock className="h-4 w-4" />}
            label="Verbleibende Zeit"
            value="20 22h 46m"
          />
          <InfoItem
            icon={<Timer className="h-4 w-4" />}
            label="Spieldauer"
            value="Dauer: 90 Minuten"
          />
        </InfoGrid>
      </InfoCard>
    </div>
  ),
};

// Card Variants Showcase
export const CardVariants: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
      <InfoCard variant="default" title="Default Card" titleIcon={<Trophy className="h-4 w-4" />}>
        <InfoItem label="Design" value="Standard mit subtilen Schatten" />
        <InfoItem label="Verwendung" value="Allgemeine Inhalte" />
      </InfoCard>
      
      <InfoCard variant="elevated" title="Elevated Card" titleIcon={<Trophy className="h-4 w-4" />}>
        <InfoItem label="Design" value="Erhöht mit stärkeren Schatten" />
        <InfoItem label="Verwendung" value="Wichtige Informationen" />
      </InfoCard>
      
      <InfoCard variant="glass" title="Glass Card" titleIcon={<Trophy className="h-4 w-4" />}>
        <InfoItem label="Design" value="Glassmorphism mit Backdrop-Blur" />
        <InfoItem label="Verwendung" value="Moderne, elegante Darstellung" />
      </InfoCard>
      
      <InfoCard variant="minimal" title="Minimal Card" titleIcon={<Trophy className="h-4 w-4" />}>
        <InfoItem label="Design" value="Minimalistisch und clean" />
        <InfoItem label="Verwendung" value="Dezente Hintergrundinformationen" />
      </InfoCard>
    </div>
  ),
};

// Status Badges Showcase
export const StatusBadges: Story = {
  render: () => (
    <InfoCard variant="elevated" title="Status Badges" titleIcon={<AlertCircle className="h-4 w-4" />}>
      <div className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Soft Variants</h4>
          <div className="flex flex-wrap gap-2">
            <StatusBadge status="success" variant="soft">Bestätigt</StatusBadge>
            <StatusBadge status="warning" variant="soft">Ausstehend</StatusBadge>
            <StatusBadge status="error" variant="soft">Abgesagt</StatusBadge>
            <StatusBadge status="info" variant="soft">Information</StatusBadge>
            <StatusBadge status="neutral" variant="soft">Neutral</StatusBadge>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Outline Variants</h4>
          <div className="flex flex-wrap gap-2">
            <StatusBadge status="success" variant="outline">Bestätigt</StatusBadge>
            <StatusBadge status="warning" variant="outline">Ausstehend</StatusBadge>
            <StatusBadge status="error" variant="outline">Abgesagt</StatusBadge>
            <StatusBadge status="info" variant="outline">Information</StatusBadge>
            <StatusBadge status="neutral" variant="outline">Neutral</StatusBadge>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Solid Variants</h4>
          <div className="flex flex-wrap gap-2">
            <StatusBadge status="success" variant="solid">Bestätigt</StatusBadge>
            <StatusBadge status="warning" variant="solid">Ausstehend</StatusBadge>
            <StatusBadge status="error" variant="solid">Abgesagt</StatusBadge>
            <StatusBadge status="info" variant="solid">Information</StatusBadge>
            <StatusBadge status="neutral" variant="solid">Neutral</StatusBadge>
          </div>
        </div>
      </div>
    </InfoCard>
  ),
};

// Responsive Grid Layout
export const ResponsiveLayout: Story = {
  render: () => (
    <div className="space-y-6">
      <InfoCard
        variant="elevated"
        title="Responsive Grid Layouts"
        titleIcon={<Users className="h-4 w-4" />}
      >
        <div className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">2-Column Grid (1 col mobile, 2 cols tablet+)</h4>
            <InfoGrid columns={2}>
              <InfoItem icon={<Calendar className="h-4 w-4" />} label="Datum" value="6. Juni 2025" />
              <InfoItem icon={<Clock className="h-4 w-4" />} label="Zeit" value="21:00 Uhr" />
              <InfoItem icon={<MapPin className="h-4 w-4" />} label="Ort" value="Soccerhalle" />
              <InfoItem icon={<Users className="h-4 w-4" />} label="Spieler" value="6 von 10" />
            </InfoGrid>
          </div>
          
          <div>
            <h4 className="font-medium mb-3">3-Column Grid (1 col mobile, 2 cols tablet, 3 cols desktop)</h4>
            <InfoGrid columns={3}>
              <InfoItem label="Anmeldungen" value="6" size="sm" />
              <InfoItem label="Benötigt" value="4" size="sm" variant="accent" />
              <InfoItem label="Warteliste" value="2" size="sm" />
            </InfoGrid>
          </div>
        </div>
      </InfoCard>
    </div>
  ),
};

// Interactive Cards
export const InteractiveCards: Story = {
  render: () => (
    <InfoGrid columns={3}>
      <InfoCard
        variant="elevated"
        interactive
        title="Hover mich!"
        titleIcon={<Trophy className="h-4 w-4" />}
        badge={<StatusBadge status="success" size="sm">Aktiv</StatusBadge>}
      >
        <InfoItem label="Interaktiv" value="Hover-Effekte aktiviert" />
      </InfoCard>
      
      <InfoCard
        variant="glass"
        interactive
        title="Glassmorphism"
        titleIcon={<Calendar className="h-4 w-4" />}
      >
        <InfoItem label="Design" value="Moderne Transparenz" />
      </InfoCard>
      
      <InfoCard
        variant="minimal"
        title="Statisch"
        titleIcon={<Clock className="h-4 w-4" />}
      >
        <InfoItem label="Interaktiv" value="Keine Hover-Effekte" />
      </InfoCard>
    </InfoGrid>
  ),
};

// Complete Game Info Example
export const GameInfoComplete: Story = {
  render: () => (
    <div className="max-w-4xl space-y-6">
      <InfoCard
        variant="elevated"
        interactive
        title="Nächstes Spiel - Vollständige Ansicht"
        titleIcon={<Calendar className="h-5 w-5" />}
        badge={<StatusBadge status="info" size="sm">Anmeldung geöffnet</StatusBadge>}
      >
        <div className="text-sm text-muted-foreground mb-4">
          Alle Informationen zum kommenden Spiel auf einen Blick
        </div>

        <InfoGrid columns={2} gap="default">
          <InfoItem
            icon={<Calendar className="h-4 w-4" />}
            label="Datum & Uhrzeit"
            value="Freitag, 6. Juni 2025 um 21:00"
          />
          <InfoItem
            icon={<MapPin className="h-4 w-4" />}
            label="Spielort"
            value="Soccerhalle Niederrhein"
          />
          <InfoItem
            icon={<Clock className="h-4 w-4" />}
            label="Verbleibende Zeit"
            value="20 Tage 22h 46m"
          />
          <InfoItem
            icon={<Timer className="h-4 w-4" />}
            label="Spieldauer"
            value="90 Minuten"
          />
        </InfoGrid>

        <div className="mt-6 pt-4 border-t border-border/30">
          <h4 className="font-semibold text-sm text-foreground/90 mb-3">Spielerstatus</h4>
          <InfoGrid columns={3} gap="sm">
            <InfoItem
              icon={<Users className="h-4 w-4" />}
              label="Anmeldungen"
              value="6 Spieler"
              size="sm"
            />
            <InfoItem
              icon={<Users className="h-4 w-4" />}
              label="Benötigt"
              value="Noch 4 Spieler"
              size="sm"
              variant="accent"
            />
            <InfoItem
              icon={<AlertCircle className="h-4 w-4" />}
              label="Status"
              value={<StatusBadge status="warning" size="sm">Noch nicht bestätigt</StatusBadge>}
              size="sm"
            />
          </InfoGrid>
        </div>

        <div className="mt-4 p-3 bg-muted/30 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <span className="font-medium text-foreground">Spielregeln</span>
              <p className="text-muted-foreground mt-1">
                Hier finden Sie alle Regeln und Informationen zum Spielablauf
              </p>
            </div>
          </div>
        </div>
      </InfoCard>
    </div>
  ),
};
