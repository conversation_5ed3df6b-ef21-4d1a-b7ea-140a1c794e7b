import React from "react";
import { Calendar, Clock, MapPin, Users, Timer, AlertCircle } from "lucide-react";
import { InfoCard, InfoGrid, InfoItem, StatusBadge } from "@/components/ui/info-card";

// Example component showing how to use the new custom cards for game information
export const GameInfoCardExample = () => {
  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      {/* Main Game Info Card */}
      <InfoCard
        variant="elevated"
        interactive
        title="Nächstes Spiel"
        titleIcon={<Calendar className="h-5 w-5" />}
        badge={<StatusBadge status="info" size="sm">Anmeldung geöffnet</StatusBadge>}
        className="w-full"
      >
        <div className="text-sm text-muted-foreground mb-4">
          Informationen zum nächsten Spiel
        </div>

        {/* Responsive Grid Layout for Maximum Content */}
        <InfoGrid columns={2} gap="default">
          <InfoItem
            icon={<Calendar className="h-4 w-4" />}
            label="Datum & Uhrzeit"
            value="Freitag, 6. Juni 2025 um 21:00"
          />
          <InfoItem
            icon={<MapPin className="h-4 w-4" />}
            label="Spielort"
            value="Soccerhalle Niederrhein"
          />
          <InfoItem
            icon={<Clock className="h-4 w-4" />}
            label="Verbleibende Zeit"
            value="20 22h 46m"
          />
          <InfoItem
            icon={<Timer className="h-4 w-4" />}
            label="Spieldauer"
            value="Dauer: 90 Minuten"
          />
        </InfoGrid>

        {/* Player Status Section */}
        <div className="mt-6 pt-4 border-t border-border/30">
          <h4 className="font-semibold text-sm text-foreground/90 mb-3">Spielerstatus</h4>
          <InfoGrid columns={3} gap="sm">
            <InfoItem
              icon={<Users className="h-4 w-4" />}
              label="Anmeldungen"
              value="6 Spieler"
              size="sm"
            />
            <InfoItem
              icon={<Users className="h-4 w-4" />}
              label="Benötigt"
              value="Noch 4 Spieler"
              size="sm"
              variant="accent"
            />
            <InfoItem
              icon={<AlertCircle className="h-4 w-4" />}
              label="Status"
              value={<StatusBadge status="warning" size="sm">Noch nicht bestätigt</StatusBadge>}
              size="sm"
            />
          </InfoGrid>
        </div>

        {/* Game Rules Section */}
        <div className="mt-4 p-3 bg-muted/30 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <span className="font-medium text-foreground">Spielregeln</span>
              <p className="text-muted-foreground mt-1">
                Hier finden Sie alle Regeln und Informationen zum Spielablauf
              </p>
            </div>
          </div>
        </div>
      </InfoCard>

      {/* Compact Cards Grid */}
      <InfoGrid columns={3} gap="default">
        <InfoCard
          variant="glass"
          title="Wetter"
          titleIcon={<Calendar className="h-4 w-4" />}
        >
          <InfoItem
            label="Temperatur"
            value="18°C"
            size="sm"
          />
          <InfoItem
            label="Bedingungen"
            value="Leicht bewölkt"
            size="sm"
          />
        </InfoCard>

        <InfoCard
          variant="minimal"
          title="Letzte Spiele"
          titleIcon={<Timer className="h-4 w-4" />}
        >
          <InfoItem
            label="Siege"
            value="3 von 5"
            size="sm"
            variant="accent"
          />
          <InfoItem
            label="Tore"
            value="12:8"
            size="sm"
          />
        </InfoCard>

        <InfoCard
          variant="default"
          title="Team Info"
          titleIcon={<Users className="h-4 w-4" />}
        >
          <InfoItem
            label="Aktive Spieler"
            value="24"
            size="sm"
          />
          <InfoItem
            label="Durchschnittsalter"
            value="28 Jahre"
            size="sm"
          />
        </InfoCard>
      </InfoGrid>

      {/* Mobile-Optimized Single Column Layout */}
      <div className="block sm:hidden">
        <InfoCard
          variant="elevated"
          title="Mobile Ansicht"
          titleIcon={<MapPin className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <InfoItem
              icon={<Calendar className="h-4 w-4" />}
              label="Nächstes Spiel"
              value="Freitag, 6. Juni 2025"
            />
            <InfoItem
              icon={<Clock className="h-4 w-4" />}
              label="Uhrzeit"
              value="21:00 Uhr"
            />
            <InfoItem
              icon={<MapPin className="h-4 w-4" />}
              label="Ort"
              value="Soccerhalle Niederrhein"
            />
            <InfoItem
              icon={<Users className="h-4 w-4" />}
              label="Status"
              value={<StatusBadge status="info">6 von 10 Spielern</StatusBadge>}
            />
          </div>
        </InfoCard>
      </div>
    </div>
  );
};

export default GameInfoCardExample;
