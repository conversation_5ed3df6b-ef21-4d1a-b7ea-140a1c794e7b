import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Card as BaseCard,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>ardFooter,
  CardTitle as BaseCardTitle,
  CardDescription as BaseCardDescription,
  CardContent as BaseCardContent,
} from "@/components/ui/card";

// Enhanced Card with modern glassmorphism design
const Card = React.forwardRef<
  React.ElementRef<typeof BaseCard>,
  React.ComponentPropsWithoutRef<typeof BaseCard> & {
    variant?: "default" | "elevated" | "glass" | "minimal";
    interactive?: boolean;
  }
>(({ className, variant = "default", interactive = false, ...props }, ref) => {
  const variants = {
    default: "bg-card/95 backdrop-blur-sm border border-border/50 shadow-lg shadow-black/5",
    elevated: "bg-card/98 backdrop-blur-md border-0 shadow-xl shadow-black/10 ring-1 ring-border/20",
    glass: "bg-card/80 backdrop-blur-lg border border-white/20 shadow-2xl shadow-black/20",
    minimal: "bg-card border border-border/30 shadow-sm"
  };

  return (
    <BaseCard
      ref={ref}
      className={cn(
        "rounded-2xl overflow-hidden transition-all duration-300 ease-out",
        variants[variant],
        interactive && "hover:shadow-xl hover:shadow-black/10 hover:-translate-y-1 cursor-pointer",
        "dark:shadow-black/25",
        className
      )}
      {...props}
    />
  );
});
Card.displayName = "Card";

// Enhanced CardHeader with better spacing and visual hierarchy
const CardHeader = React.forwardRef<
  React.ElementRef<typeof BaseCardHeader>,
  React.ComponentPropsWithoutRef<typeof BaseCardHeader> & {
    gradient?: boolean;
    spacing?: "default" | "compact" | "relaxed";
    variant?: "default" | "accent" | "minimal";
  }
>(({ className, gradient = false, spacing = "default", variant = "default", ...props }, ref) => {
  const spacingClasses = {
    compact: "px-4 py-3 space-y-1",
    default: "px-5 py-4 space-y-2",
    relaxed: "px-6 py-5 space-y-3"
  };

  const variantClasses = {
    default: "",
    accent: "border-b border-border/30",
    minimal: "pb-2"
  };

  return (
    <BaseCardHeader
      ref={ref}
      className={cn(
        spacingClasses[spacing],
        variantClasses[variant],
        gradient && "bg-gradient-to-b from-muted/30 to-transparent",
        className
      )}
      {...props}
    />
  );
});
CardHeader.displayName = "CardHeader";

// Enhanced CardTitle with better typography and icon support
const CardTitle = React.forwardRef<
  React.ElementRef<typeof BaseCardTitle>,
  React.ComponentPropsWithoutRef<typeof BaseCardTitle> & {
    icon?: React.ReactNode;
    size?: "sm" | "default" | "lg";
    accent?: boolean;
  }
>(({ className, children, icon, size = "default", accent = false, ...props }, ref) => {
  const sizeClasses = {
    sm: "text-lg font-semibold",
    default: "text-xl font-semibold",
    lg: "text-2xl font-bold"
  };

  return (
    <BaseCardTitle
      ref={ref}
      className={cn(
        "flex items-center gap-3 leading-tight tracking-tight",
        sizeClasses[size],
        "text-foreground",
        accent && "text-primary",
        className
      )}
      {...props}
    >
      {icon && (
        <span className="flex-shrink-0 text-muted-foreground/80 transition-colors">
          {icon}
        </span>
      )}
      <span className="min-w-0 flex-1">{children}</span>
    </BaseCardTitle>
  );
});
CardTitle.displayName = "CardTitle";

// Enhanced CardDescription with better readability
const CardDescription = React.forwardRef<
  React.ElementRef<typeof BaseCardDescription>,
  React.ComponentPropsWithoutRef<typeof BaseCardDescription> & {
    size?: "sm" | "default";
  }
>(({ className, size = "default", ...props }, ref) => {
  const sizeClasses = {
    sm: "text-xs",
    default: "text-sm"
  };

  return (
    <BaseCardDescription
      ref={ref}
      className={cn(
        "text-muted-foreground leading-relaxed",
        sizeClasses[size],
        className
      )}
      {...props}
    />
  );
});
CardDescription.displayName = "CardDescription";

// Enhanced CardContent with responsive spacing
const CardContent = React.forwardRef<
  React.ElementRef<typeof BaseCardContent>,
  React.ComponentPropsWithoutRef<typeof BaseCardContent> & {
    spacing?: "none" | "compact" | "default" | "relaxed";
    grid?: boolean;
  }
>(({ className, spacing = "default", grid = false, ...props }, ref) => {
  const spacingClasses = {
    none: "p-0",
    compact: "px-4 py-3",
    default: "px-5 py-4",
    relaxed: "px-6 py-5"
  };

  return (
    <BaseCardContent
      ref={ref}
      className={cn(
        spacingClasses[spacing],
        grid && "grid gap-4",
        className
      )}
      {...props}
    />
  );
});
CardContent.displayName = "CardContent";

// Enhanced CardFooter with better alignment options
const CardFooter = React.forwardRef<
  React.ElementRef<typeof BaseCardFooter>,
  React.ComponentPropsWithoutRef<typeof BaseCardFooter> & {
    spacing?: "compact" | "default" | "relaxed";
    justify?: "start" | "center" | "end" | "between";
    variant?: "default" | "accent" | "minimal";
  }
>(({ className, spacing = "default", justify = "end", variant = "default", ...props }, ref) => {
  const spacingClasses = {
    compact: "px-4 py-3 pt-2",
    default: "px-5 py-4 pt-2",
    relaxed: "px-6 py-5 pt-3"
  };

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between"
  };

  const variantClasses = {
    default: "",
    accent: "border-t border-border/30 bg-muted/20",
    minimal: "pt-0"
  };

  return (
    <BaseCardFooter
      ref={ref}
      className={cn(
        "flex items-center gap-3",
        spacingClasses[spacing],
        justifyClasses[justify],
        variantClasses[variant],
        className
      )}
      {...props}
    />
  );
});
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
