import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/custom-card";

// Specialized InfoCard for displaying game information with optimal space usage
interface InfoCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "elevated" | "glass" | "minimal";
  interactive?: boolean;
  title?: string;
  titleIcon?: React.ReactNode;
  badge?: React.ReactNode;
  children: React.ReactNode;
}

const InfoCard = React.forwardRef<HTMLDivElement, InfoCardProps>(
  ({ className, variant = "elevated", interactive = false, title, titleIcon, badge, children, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        variant={variant}
        interactive={interactive}
        className={cn("w-full", className)}
        {...props}
      >
        {(title || badge) && (
          <CardHeader 
            spacing="compact" 
            variant={badge ? "accent" : "minimal"}
            className="relative"
          >
            <div className="flex items-center justify-between">
              {title && (
                <CardTitle 
                  size="sm" 
                  icon={titleIcon}
                  className="text-foreground/90"
                >
                  {title}
                </CardTitle>
              )}
              {badge && (
                <div className="flex-shrink-0">
                  {badge}
                </div>
              )}
            </div>
          </CardHeader>
        )}
        <CardContent spacing="compact" className="space-y-4">
          {children}
        </CardContent>
      </Card>
    );
  }
);
InfoCard.displayName = "InfoCard";

// InfoGrid for responsive layout of information items
interface InfoGridProps extends React.HTMLAttributes<HTMLDivElement> {
  columns?: 1 | 2 | 3 | 4;
  gap?: "sm" | "default" | "lg";
  children: React.ReactNode;
}

const InfoGrid = React.forwardRef<HTMLDivElement, InfoGridProps>(
  ({ className, columns = 2, gap = "default", children, ...props }, ref) => {
    const gapClasses = {
      sm: "gap-3",
      default: "gap-4",
      lg: "gap-6"
    };

    const columnClasses = {
      1: "grid-cols-1",
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
    };

    return (
      <div
        ref={ref}
        className={cn(
          "grid w-full",
          columnClasses[columns],
          gapClasses[gap],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
InfoGrid.displayName = "InfoGrid";

// InfoItem for individual pieces of information with icon and label
interface InfoItemProps extends React.HTMLAttributes<HTMLDivElement> {
  icon?: React.ReactNode;
  label: string;
  value: React.ReactNode;
  variant?: "default" | "accent" | "muted";
  size?: "sm" | "default";
}

const InfoItem = React.forwardRef<HTMLDivElement, InfoItemProps>(
  ({ className, icon, label, value, variant = "default", size = "default", ...props }, ref) => {
    const variantClasses = {
      default: "text-foreground",
      accent: "text-primary",
      muted: "text-muted-foreground"
    };

    const sizeClasses = {
      sm: "text-sm",
      default: "text-base"
    };

    return (
      <div
        ref={ref}
        className={cn("flex items-start gap-3 min-w-0", className)}
        {...props}
      >
        {icon && (
          <div className="flex-shrink-0 mt-0.5 text-muted-foreground/70">
            {icon}
          </div>
        )}
        <div className="min-w-0 flex-1 space-y-1">
          <div className={cn("font-medium leading-tight", sizeClasses[size], variantClasses[variant])}>
            {label}
          </div>
          <div className={cn("text-muted-foreground leading-relaxed", size === "sm" ? "text-xs" : "text-sm")}>
            {value}
          </div>
        </div>
      </div>
    );
  }
);
InfoItem.displayName = "InfoItem";

// StatusBadge for displaying status information
interface StatusBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  status: "success" | "warning" | "error" | "info" | "neutral";
  size?: "sm" | "default";
  variant?: "solid" | "outline" | "soft";
  children: React.ReactNode;
}

const StatusBadge = React.forwardRef<HTMLDivElement, StatusBadgeProps>(
  ({ className, status, size = "default", variant = "soft", children, ...props }, ref) => {
    const statusClasses = {
      success: {
        solid: "bg-green-600 text-white",
        outline: "border-green-600 text-green-600",
        soft: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      },
      warning: {
        solid: "bg-amber-600 text-white",
        outline: "border-amber-600 text-amber-600",
        soft: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
      },
      error: {
        solid: "bg-red-600 text-white",
        outline: "border-red-600 text-red-600",
        soft: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      },
      info: {
        solid: "bg-blue-600 text-white",
        outline: "border-blue-600 text-blue-600",
        soft: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
      },
      neutral: {
        solid: "bg-gray-600 text-white",
        outline: "border-gray-600 text-gray-600",
        soft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
      }
    };

    const sizeClasses = {
      sm: "px-2 py-1 text-xs",
      default: "px-3 py-1.5 text-sm"
    };

    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center rounded-full font-medium",
          sizeClasses[size],
          statusClasses[status][variant],
          variant === "outline" && "border",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
StatusBadge.displayName = "StatusBadge";

export { InfoCard, InfoGrid, InfoItem, StatusBadge };
