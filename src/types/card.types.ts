// Type definitions for enhanced custom cards

export type CardVariant = "default" | "elevated" | "glass" | "minimal";
export type CardSpacing = "none" | "compact" | "default" | "relaxed";
export type CardSize = "sm" | "default" | "lg";
export type CardJustify = "start" | "center" | "end" | "between";
export type StatusType = "success" | "warning" | "error" | "info" | "neutral";
export type BadgeVariant = "solid" | "outline" | "soft";

// Enhanced Card Props
export interface EnhancedCardProps {
  variant?: CardVariant;
  interactive?: boolean;
}

// Card Header Props
export interface CardHeaderProps {
  gradient?: boolean;
  spacing?: Exclude<CardSpacing, "none">;
  variant?: "default" | "accent" | "minimal";
}

// Card Title Props
export interface CardTitleProps {
  icon?: React.ReactNode;
  size?: CardSize;
  accent?: boolean;
}

// Card Description Props
export interface CardDescriptionProps {
  size?: Exclude<CardSize, "lg">;
}

// Card Content Props
export interface CardContentProps {
  spacing?: CardSpacing;
  grid?: boolean;
}

// Card Footer Props
export interface CardFooterProps {
  spacing?: Exclude<CardSpacing, "none">;
  justify?: CardJustify;
  variant?: "default" | "accent" | "minimal";
}

// Info Card Props
export interface InfoCardProps {
  variant?: CardVariant;
  interactive?: boolean;
  title?: string;
  titleIcon?: React.ReactNode;
  badge?: React.ReactNode;
}

// Info Grid Props
export interface InfoGridProps {
  columns?: 1 | 2 | 3 | 4;
  gap?: "sm" | "default" | "lg";
}

// Info Item Props
export interface InfoItemProps {
  icon?: React.ReactNode;
  label: string;
  value: React.ReactNode;
  variant?: "default" | "accent" | "muted";
  size?: Exclude<CardSize, "lg">;
}

// Status Badge Props
export interface StatusBadgeProps {
  status: StatusType;
  size?: Exclude<CardSize, "lg">;
  variant?: BadgeVariant;
}

// Game Information Types (for your specific use case)
export interface GameInfo {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  duration: string;
  playersRegistered: number;
  playersNeeded: number;
  status: StatusType;
  description?: string;
}

export interface PlayerStatus {
  registered: number;
  needed: number;
  confirmed: boolean;
  status: StatusType;
}

export interface GameRules {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

// Responsive Breakpoints for Cards
export const CARD_BREAKPOINTS = {
  mobile: 'max-w-sm',
  tablet: 'max-w-md',
  desktop: 'max-w-lg',
  wide: 'max-w-xl'
} as const;

// Card Animation Presets
export const CARD_ANIMATIONS = {
  none: '',
  subtle: 'transition-all duration-200 ease-out',
  smooth: 'transition-all duration-300 ease-out',
  bouncy: 'transition-all duration-300 ease-bounce'
} as const;

// Card Shadow Presets
export const CARD_SHADOWS = {
  none: 'shadow-none',
  sm: 'shadow-sm',
  default: 'shadow-lg shadow-black/5',
  elevated: 'shadow-xl shadow-black/10',
  dramatic: 'shadow-2xl shadow-black/20'
} as const;
