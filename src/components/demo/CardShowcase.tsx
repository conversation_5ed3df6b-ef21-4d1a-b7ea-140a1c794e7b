import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/custom-card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  Zap,
  Gamepad2,
  Cpu,
  Rocket,
  Target,
  Flame,
  Shield,
  Swords,
  Crown,
  Bolt,
  Sparkles,
  Hexagon
} from "lucide-react";

/**
 * CYBER SPORT SHOWCASE - Völlig neue Designsprache!
 * Neon-Cyber-Gaming-Ästhetik mit futuristischen Effekten
 */
function CardShowcaseContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-slate-950 to-black dark:from-black dark:via-slate-950 dark:to-black p-4 sm:p-6 lg:p-8 relative overflow-hidden">
      {/* Animated Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(6,182,212,0.1),transparent_50%)] animate-pulse"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(147,51,234,0.1),transparent_50%)] animate-pulse delay-1000"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(16,185,129,0.1),transparent_50%)] animate-pulse delay-2000"></div>

      <div className="max-w-7xl mx-auto space-y-12 relative z-10">

        {/* Epic Header */}
        <div className="text-center space-y-6">
          <div className="relative">
            <h1 className="text-6xl font-black bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent drop-shadow-[0_0_30px_rgba(6,182,212,0.5)] animate-pulse">
              ⚡ CYBER SPORT CARDS ⚡
            </h1>
            <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/20 via-purple-500/20 to-pink-500/20 blur-xl animate-pulse"></div>
          </div>
          <p className="text-xl text-cyan-100/80 max-w-3xl mx-auto font-bold tracking-wide drop-shadow-[0_0_10px_rgba(6,182,212,0.3)]">
            🚀 NEXT-LEVEL GAMING UI • NEON CYBER AESTHETICS • ULTRA RESPONSIVE 🚀
          </p>
        </div>

        {/* ULTIMATE GAME CARD - NEON STYLE */}
        <section className="space-y-8">
          <div className="text-center">
            <h2 className="text-4xl font-black text-cyan-100 drop-shadow-[0_0_20px_rgba(6,182,212,0.5)] mb-2">
              ⚡ ULTIMATE GAME INTERFACE ⚡
            </h2>
            <p className="text-cyan-200/70 text-lg font-semibold">Next-Level Gaming Experience</p>
          </div>

          <Card variant="neon" size="xl" interactive glow className="max-w-5xl mx-auto">
            <CardHeader variant="accent" spacing="relaxed">
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <CardTitle icon={<Trophy className="w-6 h-6" />} size="xl">
                    Nächstes Spiel
                  </CardTitle>
                  <CardDescription size="lg">
                    Informationen zum nächsten Spiel
                  </CardDescription>
                </div>
                <Badge className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2">
                  Anmeldung geöffnet
                </Badge>
              </div>
              <Separator className="bg-blue-200/50 dark:bg-blue-800/30" />
            </CardHeader>

            <CardContent variant="spacious" spacing="relaxed">
              {/* Game Information Grid */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Allgemeine Spielinformationen:
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/20 p-4 rounded-xl border border-blue-100/50 dark:border-blue-900/30">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-full shadow-sm">
                      <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Datum & Uhrzeit:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Freitag, 6. Juni 2025 um 21:00</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/20 p-4 rounded-xl border border-green-100/50 dark:border-green-900/30">
                    <div className="bg-green-100 dark:bg-green-900/50 p-3 rounded-full shadow-sm">
                      <MapPin className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Spielort:</p>
                      <p className="text-sm text-blue-600 dark:text-blue-400 hover:underline cursor-pointer">
                        Soccerhalle Niederrhein
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/20 p-4 rounded-xl border border-purple-100/50 dark:border-purple-900/30">
                    <div className="bg-purple-100 dark:bg-purple-900/50 p-3 rounded-full shadow-sm">
                      <Timer className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Verbleibende Zeit:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">20 22h 46m</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/20 p-4 rounded-xl border border-amber-100/50 dark:border-amber-900/30">
                    <div className="bg-amber-100 dark:bg-amber-900/50 p-3 rounded-full shadow-sm">
                      <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Spieldauer:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Dauer: 90 Minuten</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Player Status */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Spielerstatus:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/20 p-4 rounded-xl border border-blue-100/50 dark:border-blue-900/30">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-full shadow-sm">
                      <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Anmeldungen:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">6 Spieler</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/20 p-4 rounded-xl border border-green-100/50 dark:border-green-900/30">
                    <div className="bg-green-100 dark:bg-green-900/50 p-3 rounded-full shadow-sm">
                      <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Benötigt:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Noch 4 Spieler</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-950/30 dark:to-yellow-950/20 p-4 rounded-xl border border-amber-100/50 dark:border-amber-900/30">
                    <div className="bg-amber-100 dark:bg-amber-900/50 p-3 rounded-full shadow-sm">
                      <Star className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">Status:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Noch nicht bestätigt</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>

            <CardFooter variant="accent" justify="between">
              <Button variant="outline" className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Spielregeln
              </Button>
              <Button className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
                <CreditCard className="h-4 w-4" />
                Platzgebühr bezahlen (15 €)
              </Button>
            </CardFooter>
          </Card>
        </section>

        {/* Card Variants Grid */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            🎨 Card Varianten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

            {/* Default Card */}
            <Card variant="default" size="md">
              <CardHeader>
                <CardTitle size="md">Default Card</CardTitle>
                <CardDescription>Standard Design mit subtilen Schatten</CardDescription>
              </CardHeader>
              <CardContent variant="compact">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Perfekt für die meisten Anwendungsfälle mit ausgewogener Optik.
                </p>
              </CardContent>
            </Card>

            {/* Elevated Card */}
            <Card variant="elevated" size="md">
              <CardHeader>
                <CardTitle size="md">Elevated Card</CardTitle>
                <CardDescription>Schwebender Effekt mit starken Schatten</CardDescription>
              </CardHeader>
              <CardContent variant="compact">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Ideal für wichtige Inhalte, die hervorgehoben werden sollen.
                </p>
              </CardContent>
            </Card>

            {/* Glass Card */}
            <Card variant="glass" size="md">
              <CardHeader>
                <CardTitle size="md">Glass Card</CardTitle>
                <CardDescription>Glassmorphism-Effekt mit Backdrop-Blur</CardDescription>
              </CardHeader>
              <CardContent variant="compact">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Moderner Glaseffekt für zeitgemäße Designs.
                </p>
              </CardContent>
            </Card>

            {/* Gradient Card */}
            <Card variant="gradient" size="md">
              <CardHeader>
                <CardTitle size="md">Gradient Card</CardTitle>
                <CardDescription>Subtile Farbverläufe für mehr Tiefe</CardDescription>
              </CardHeader>
              <CardContent variant="compact">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Elegante Farbverläufe für besondere Akzente.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Interactive Cards */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            🖱️ Interaktive Cards
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            <Card variant="elevated" size="lg" interactive className="cursor-pointer">
              <CardHeader variant="accent">
                <CardTitle icon={<Trophy className="w-5 h-5" />} size="lg">
                  Klickbare Card
                </CardTitle>
                <CardDescription>Hover- und Click-Effekte inklusive</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Diese Card reagiert auf Maus-Interaktionen mit sanften Animationen.
                </p>
              </CardContent>
            </Card>

            <Card variant="glass" size="lg" interactive>
              <CardHeader variant="clean">
                <CardTitle icon={<Star className="w-5 h-5" />} size="lg">
                  Glass Interactive
                </CardTitle>
                <CardDescription>Glaseffekt mit Interaktivität</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Kombiniert moderne Glasoptik mit responsiven Hover-Effekten.
                </p>
              </CardContent>
            </Card>

            <Card variant="gradient" size="lg" interactive>
              <CardHeader variant="minimal">
                <CardTitle icon={<Users className="w-5 h-5" />} size="lg">
                  Gradient Interactive
                </CardTitle>
                <CardDescription>Farbverläufe mit Animation</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Elegante Farbverläufe mit subtilen Hover-Animationen.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Mobile Responsiveness Demo */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            📱 Mobile Responsiveness
          </h2>
          <Card variant="elevated" size="xl" className="max-w-2xl mx-auto">
            <CardHeader variant="accent" spacing="relaxed">
              <CardTitle size="xl">Mobile-Optimiert</CardTitle>
              <CardDescription size="lg">
                Alle Cards sind vollständig responsive und mobile-optimiert
              </CardDescription>
            </CardHeader>
            <CardContent variant="spacious" spacing="relaxed">
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/20 p-4 rounded-xl">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                    ✨ Mobile Features:
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li>• Optimierte Touch-Targets (min. 44px)</li>
                    <li>• Responsive Spacing und Padding</li>
                    <li>• Flexible Grid-Layouts</li>
                    <li>• Verbesserte Lesbarkeit auf kleinen Bildschirmen</li>
                    <li>• Sanfte Animationen ohne Performance-Probleme</li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter variant="accent" justify="center">
              <Badge className="bg-green-500 text-white px-4 py-2">
                100% Mobile Ready 🚀
              </Badge>
            </CardFooter>
          </Card>
        </section>

      </div>
    </div>
  );
}

/**
 * Main CardShowcase page component with Header and Footer
 */
export default function CardShowcase() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header activePage="card-showcase" />
      <main className="flex-1">
        <CardShowcaseContent />
      </main>
      <Footer />
    </div>
  );
}
